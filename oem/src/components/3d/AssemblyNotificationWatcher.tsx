import { useCallback, useEffect } from "react";

import { useAuth } from "../general";

import { NOTIFICATION_IDENTIFIERS } from "#/src/constants/notification-identifiers";

const AssemblyNotificationWatcher = ({
  onMessageReceived,
  machineId,
}: {
  onMessageReceived: (success: boolean, machineId: string) => void;
  machineId: string;
}) => {
  const auth = useAuth();

  // @ts-ignore
  const chatInstance = auth.chat as PubNub;

  const onMessage = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async (event: any) => {
      if (
        [NOTIFICATION_IDENTIFIERS.ASSEMBLY_ZIP_FILE_PROCESSING].includes(
          event?.message?.text,
        )
      ) {
        console.log("Received message:", event);
        onMessageReceived(event?.message?.success ?? false, machineId);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  useEffect(() => {
    if (chatInstance) chatInstance.addListener({ message: onMessage });

    return () =>
      chatInstance && chatInstance.removeListener({ message: onMessage });
  }, [chatInstance, onMessage]);

  return null;
};

export default AssemblyNotificationWatcher;
