{"totalMachineNumber": "Assets", "newMachineButton": "Add a Machine", "noMachines": "There is no machine added.", "serialNum": "Serial Number", "kanbanSerialNum": "SN", "templateRevert": {"cta": "Revert to Product", "modalDesc": "Select which components you want to revert", "description": "Description", "image": "Image", "inventoryParts": "Parts", "documentation": "Documents", "info": "Reverting will overwrite the selected components with product data. ", "btnCta": "<PERSON><PERSON>", "cancel": "Cancel", "reverting": "Reverting to Product", "revertingDsc": "We’re reconnecting this asset back to the product and overwriting your changes. This might take a moment.", "success": "Reverted Component to product", "successAll": "Reverted Asset to product"}, "modelCreation": "Simply choose whether you're adding a model for an asset or a product, upload your file, and it will appear on the respective details page under '3D model'.", "myAssets": "My Assets", "myProducts": "My Products", "machineDetails": {"titleTabs": {"details": "Details", "documentation": "Documentation", "history": "History", "parts": "Parts", "preventiveMaintenance": "Preventive Maintenance", "generalInfo": "General Information", "components": "Components", "_3D": "3D Model", "subAssets": "Sub Assets"}, "detailsTab": {"qrCode": {"title": "Machine QR code access", "message": "This allows the user to access the machine data using the assigned QR code instead of the login credentials provided.", "status": {"disabled": "DISABLED", "enabled": "ENABLED"}, "copyUrl": "Copy URL", "copyTooltip": "<PERSON>pied", "checkHasFacilityAssigned": "You have to assign this machine to a facility first", "nameAndNumber": "Show machine name & serial number", "displayLogo": "Display Logo", "machineName": "Machine Name:", "serialNumber": "Serial Number:", "printQRCode": "Print QR code"}, "serialNum": "SN", "facility": "Facility", "connection": "Connection", "assetType": "Asset Type", "parentAsset": "<PERSON><PERSON>", "productionLine": "Production line", "team": "Team:", "installationDate": "Installation date", "descriptionLabel": "Description", "empty": "Empty", "edit": {"title": "Edit", "access": "Access", "labels": {"nameLabel": "Machine Name *", "nameError": "Name can not be more than 75 characters", "serialNum": "Serial Number *", "serialError": "Serial number can not be more than 75 characters", "facilityLabel": "Facility", "facilityPlaceholder": "Facility", "installationDate": "Installation Date", "description": "Machine Description", "descriptionPlaceholder": "Add machine description"}, "qrCodeMessage": "This allows reporters to open an app with scanning the machine QR code instead using sing in credentials.", "qrStatusSuccessMsgStart": "Machine QR Code", "qrStatusEnableMsg": "enabled", "qrStatusDisableMsg": "disabled", "imageUpload": {"dropImage": {"beforeLink": "Drop your image here, or ", "link": "click to browse", "afterLink": " on your computer"}, "fileRecommendation": "1600 x 1200 (4:3) recommended, up to 10MB.", "deleteImg": "Delete Image", "editImg": "Edit Image", "replaceImg": "Replace Image", "fileTooLarge": "Image is too large. Image size should be less than 10MB"}, "delete": {"button": "Delete Machine", "window": {"title": "Are you sure you want to delete this machine? This action cannot be reversed", "yes": "Yes, Delete", "no": "No, Cancel"}}, "nameError": "Machine Name is required", "serialNumError": "Serial Number is required", "imageUploadError": "Something went wrong with image upload", "imageDeleteSuccess": "Machine image deleted successfully", "updateSuccess": "Machine updated successfully", "updateFailed": "Failed to update machine", "deleteSuccess": "Machine deleted successfully"}}, "historyTab": {"showToFacility": "Show to connection", "noHistoryMessage": "There is no machine history for this machine.", "ticketsRelated": "work orders related to this machine", "tickedCreationDate": "creation date:", "tickedId": "Work Order ID:", "machineCreationDate": "Machine Creation Date:", "addNote": "Add a note...", "editNote": "Edit note", "deleteNote": "Delete note", "noteMessages": {"addingNote": "Adding note...", "updatingNote": "Updating note...", "createSuccess": "Note added successfully", "deleteSuccess": "Note deleted successfully", "updateSuccess": "Note updated successfully", "deleteAlert": {"title": "Delete note?", "message": "Are you sure you want to continue? This action cannot be undone."}}}, "machine3DTab": {"supportedFileText": "Supported formats: ", "_3DModelAssetDeleteSuccess": "Asset 3D model deleted successfully", "_3DModelDeleteSuccess": "Machine 3D model deleted successfully", "welcomeText": "Welcome to 3D model!", "welcomeDesc1": "The 3D model for the machine is currently disabled for your account. To unlock the full potential of this powerful tool and revolutionize your experience, please contact our support team at", "welcomeDesc2": "In the meantime, we invite you to explore a demo of the 3D model to get a glimpse of its capabilities. Click the button below to launch the demo and discover the exciting possibilities.", "launchButtonText": "Launch 3D model demo", "deleteAlert": {"title": "Delete file?", "message": "Are you sure you want to delete the 3D model? This will permanently delete the file"}, "supportedFormatsModal": {"title": "List of supported formats", "formatGroup": "Format group", "supportedFileFormats": "Supported file formats", "open": {"description": "Open source formats"}, "cad": {"description": "Standard formats for data exchange between systems"}}}}, "detailExportModal": {"asset_parts": {"text": "Parts", "description": "Get a detailed breakdown of parts attached to Products."}, "parts": {"text": "Parts", "description": "Get a detailed breakdown of parts attached to Assets."}, "pme": {"text": "Preventive Maintenance Events", "description": "Get a detailed breakdown of Preventive Maintenance Events."}, "history": {"text": "History", "description": "Get a detailed breakdown of History for all Assets"}}, "created": "Machine created successfully", "creationFailed": "Machine creation failed", "serialNumberExistError": "Machine serial number already exist", "ticketID": "Work order ID", "searchPlaceholder": "Search by name/Sr. No.", "templateSearchPlaceholder": "Search by title", "addNewTemplateBtn": "Add a Template", "addNewProductionLineBtn": "Add a production line", "addNewComponentBtn": "Add a component", "noProductionLines": "There is no production line available for use.", "noMachineTemplates": "There is no machine template available for use.", "machineSubNav": {"machines": "Assets", "allMachines": "Machines", "assetTemplates": "My Products", "machineTemplates": "Machine templates", "productionLines": "Production lines", "components": "Components"}, "machineTemplateType": {"machineTemplate": "Machine Template", "lineTemplate": "Line Template"}, "machineTemplateInfo": {"title": "This is a machine template, if you create a new machine based on this template:", "bullet1": "All documentation of the newly created machine will be synced with the documentation from the template.", "bullet2": "The machine picture and description will be synced with the picture and description from the template."}, "lineTemplateInfo": {"title": "This is a line template, if you create a new machine based on this template:", "bullet1": "All documentation of the newly created machine will be synced with the documentation from the template.", "bullet2": "Machine picture and description have to be created for the machine separately."}, "templateAccessDeniedModalTitle": "If you want to use the machine template feature, please contact", "oemTemplateCreated": "Template created successfully", "oemTemplateUpdated": "Template updated successfully", "oemTemplateCreationFailed": "Template creation failed", "oemTemplateUpdationFailed": "Template update failed", "templateImageDeleteSuccess": "Template image deleted successfully", "createdMachines": "Created machines", "templateError": "Template is required", "templateDocumentation": "Template documentation", "templateDetails": "Template details", "externalDocuments": "External documents", "internalDocuments": "Internal documents", "totalMachineCreatedLabel": "machines have been created from this template", "templateDeleteAlertText": "Are you sure you want to delete this template?", "templateIdError": "ID is required", "templateTitleError": "Title is required", "totalPartsAdded": "parts added for this machine", "searchMachineParts": "Search machine parts", "addPartsToMachine": "Add Parts To Machine", "machinePartsAdded": "Parts added successfully", "machinePartsAddFailed": "Failed to add parts for this machine", "machinePartRemoved": "Parts removed successfully", "machinePartRemoveFailed": "Failed to remove parts from this machine", "noMachinePartsFound": "There is no parts assigned to this machine.", "showQrCode": "Show QR code", "useMachineQrCode": "Use machine QR Code ", "qrScanSignOutInfo": "By signing in to this facility, you will be signed out from the current one.", "machineText": "Machine", "machineNameText": "Machine name", "templateText": "Template", "new3DModelButton": "Create Model", "new3DGuideButton": "Create Guide", "3dDemoDisclaimer": "This is a demo feature. Upgrade your account to access this feature or contact sales!", "no3DModels": "No models created yet!", "submitModel": "Create Model", "saveGuide": "Save Guide", "guideNamePlaceholder": "Enter a name", "savingGuide": "Saving Guide", "guideNameLabel": "Guide Name", "guideCreationSuccess": "Your guide has been saved successfully!", "guideSaveMessage": "Please choose whether you want to save this guide to the existing file or create a new guide.", "guideSaveExisting": "Save to Existing Guide", "guideCreateNew": "Create New Guide", "deleteModelConfirmation": "Are you sure you want to delete this model?", "deleteGuideConfirmation": "Are you sure you want to delete this guide?", "guideDeletionSuccessful": "Guide deleted successfully", "deleteModelUnableMessage": "This model can only be deleted if all the guides created on this model are deleted first.", "newGuideUnsavedChangesTitle": "Are you sure you want to quit without saving? Changes that you made will not be saved.", "guideSearchPlaceholder": "Search by Name", "selectAsset": "Select Asset", "chooseAsset": "<PERSON><PERSON>", "uploadModel": "Upload Model", "uploadParts": "Upload Parts", "greeting3DModel": "Welcome to 3D Model!", "machineSelection": "Select a machine first", "subGreeting3DModel": "See your machines come alive in a stunning 3D view, allowing for inspection from any angle. Gain a deeper understanding of your machine's functionality.", "machineTemplateParts": {"assignPartsTitle": "Add parts to machine template", "noPartsFound": "There are no parts assigned to this machine template", "unassignPart": "Unassign part", "unassignMessage": "Unassigning parts will affect all machines created from this template. Are you sure you want to continue?"}, "detachedMessages": {"description": "Description has been detached from machine template", "image": "Image has been detached from machine template", "parts": "Parts have been detached from machine template.", "documentation": "Documentation has been detached from machine template."}, "detachAlerts": {"description": {"title": "Update description?", "message": "Updating machine description will detach it from the the template. Any update in the template for description will not be reflected in this machine. Do you want to continue? This action cannot be undone."}, "image": {"title": "Update image?", "message": "Updating machine image will detach it from the the template. Any update in the template for image will not be reflected in this machine. Do you want to continue? This action cannot be undone."}, "parts": {"title": "Assign parts?", "message": "Assigning parts to this machine will detach this section from machine template.  Any update in the template for parts will not be reflected in this machine. Do you want to proceed? This action cannot be undone."}, "deleteImage": {"title": "Delete image?", "message": "Deleting machine image will detach it from the the template. Any update in the template for image will not be reflected in this machine. Do you want to continue? This action cannot be undone."}, "deleteParts": {"title": "Unassign parts?", "message": "Unassigning parts from this machine will detach this section from machine template.  Any update in the template for parts will not be reflected in this machine. Do you want to proceed? This action cannot be undone."}}, "_3DModelLoadingAlert": {"title": "Heads up!", "message": "Please wait while we get your 3D model ready. This can take long for large files."}, "exportHistory": "Export History", "noModelFound": "No model found", "noGuideFound": "No guide found", "selectAModel": "Select a model", "selectModel": "Select model", "aiButtonTooltip": "Chat with AI Assistant", "threedyButtonTooltip": "View 3D Model", "disconnectDocumentations": {"title": "Disconnecting Documentation", "question": "Disconnect Documentation?", "description": "Disconnecting documents from this machine will detach this asset from machine template. Any update in the template for documents will not be reflected in this machine. Do you want to proceed? This action cannot be undone.", "message": "We're currently disconnecting the documentation from this machine. Any future updates made to the template documentation will not reflect in this asset documentation."}, "detachingDocumentationToastMsg": "Disconnecting Documentation, please wait!", "detachDocumentationFail": "Failed to disconnect the documentation", "detachDocumentationSuccess": "Documentation is disconnected successfully", "singleModelTitle": "Single Model", "singleModelDescription": "Upload one 3D file containing the complete model.", "assemblyModelTitle": "Assembly Model", "assemblyModelDescription": "Upload a ZIP with multiple 3D parts.", "modelType": "Model Type", "selectParentFile": "Select Parent File", "emptyZipFileErrorMessage": "No files found in the zip archive.", "zipFilePreviewErrorMessage": "Failed to process zip file. Please ensure it's a valid zip archive.", "emptySearchResultMessage": "No files match your search criteria.", "selectParentFileCaption": "Select the main assembly file (top-level file) from your ZIP.", "searchParentFile": "Search parent file"}