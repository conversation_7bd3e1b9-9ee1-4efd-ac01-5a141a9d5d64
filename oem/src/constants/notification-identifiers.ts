export const NOTIFICATION_IDENTIFIERS = {
  AI_ASSISTANT_INDEXING_PROGRESS: "aiAssistantIndexingProgress",
  CSV_DOWNLOAD: "csvExport",
  DROMO_UPLOAD: "dromoImport",
  PROCEDURE_ATTACH: "attachOwnOemProcedureToWorkOrder",
  PROCEDURE_FINALIZE: "finalizeOwnOemProcedure",
  PROCEDURE_PDF_DOWNLOAD: "downloadProcedurePDF",
  PROCEDURE_TEMPLATE_DUPLICATE: "duplicateOwnOemProcedureTemplate",
  SEND_EMAIL: "sendEmail",
  EMAIL_ACCOUNT_SYNCED: "emailAccountSynced",
  EMAIL_FOLDER_COUNT_SYNCED: "emailFolderCountSynced",
  SCHEDULER_DRAFT_SYNC: "schedulerDraftSync",
  TEAM_CHANGE: "teamChange",
  SEARCH_RESULT_GENERATED: "searchResultGenerated",
  ASSET_DOCUMENTATION_DETACHING: "assetDocumentationDetaching",
  KNOWLEDGE_BASE_DOCUMENT_STATUS_UPDATE: "knowledgeBaseDocumentStatusUpdate",
  KNOWLEDGE_BASE_SCAN_LIMIT_REACHED: "knowledgeBaseScanLimitReached",
  KNOWLEDGE_BASE_DOCUMENT_CUSTOM_FIELDS_UPDATE:
    "knowledgeBaseDocumentCustomFieldsUpdate",
  KNOWLEDGE_BASE_DOCUMENT_META_UPDATE: "knowledgeBaseDocumentMetaUpdate",
  KNOWLEDGE_BASE_DOCUMENT_AUTHORS_UPDATE: "knowledgeBaseDocumentAuthorsUpdate",
  KNOWLEDGE_BASE_DOCUMENT_DATE_UPDATE: "knowledgeBaseDocumentDateUpdate",
  KNOWLEDGE_BASE_DOCUMENT_TITLE_UPDATE: "knowledgeBaseDocumentTitleUpdate",
  ASSEMBLY_ZIP_FILE_PROCESSING: "process3dZipFile",
};
